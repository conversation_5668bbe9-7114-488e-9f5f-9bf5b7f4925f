using AutoMapper;
using BlueTape.Common.ExceptionHandling.Models;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.PaymentService.API.ViewModels;
using BlueTape.PaymentService.Application.Abstractions.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics.CodeAnalysis;

namespace BlueTape.PaymentService.API.Controllers;

[ExcludeFromCodeCoverage(Justification = "Controllers are not required to be tested at the moment")]
[Authorize]
[ApiController]
[Route("paymentRequests")]
public class QueuesController : ControllerBase
{
    private readonly IPaymentQueueService _paymentQueueService;
    private readonly IMapper _mapper;

    public QueuesController(IPaymentQueueService paymentQueueService, IMapper mapper)
    {
        _paymentQueueService = paymentQueueService;
        _mapper = mapper;
    }

    /// <summary>
    /// Gets disbursement queues payment requests
    /// </summary>
    /// <param name="provider">The payment provider (aion, cbw)</param>
    /// <param name="subscriptionCode">The subscription code of the payment provider as string</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The queues with requests, without pagination</returns>
    [HttpGet("disbursementQueues")]
    [ProducesResponseType(typeof(IEnumerable<PaymentRequestViewModel>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status400BadRequest)]
    public async Task<IEnumerable<PaymentRequestViewModel>> GetDisbursementQueuesPaymentRequests(
        [FromQuery] string provider,
        [FromQuery] string subscriptionCode,
        CancellationToken cancellationToken)
    {
        // Validate provider parameter
        if (!IsValidProvider(provider))
        {
            throw new ArgumentException("Invalid provider. Must be 'aion' or 'cbw'.", nameof(provider));
        }

        // Parse subscription code string to enum
        if (!Enum.TryParse<PaymentSubscriptionType>(subscriptionCode, true, out var parsedSubscriptionCode))
        {
            throw new ArgumentException($"Invalid subscription code '{subscriptionCode}'. Must be a valid PaymentSubscriptionType value.", nameof(subscriptionCode));
        }

        var paymentRequests = await _paymentQueueService.GetDisbursementQueuesPaymentRequests(provider, parsedSubscriptionCode, cancellationToken);
        return _mapper.Map<IEnumerable<PaymentRequestViewModel>>(paymentRequests);
    }

    private static bool IsValidProvider(string provider)
    {
        return provider?.ToLowerInvariant() is "aion" or "cbw";
    }
}
