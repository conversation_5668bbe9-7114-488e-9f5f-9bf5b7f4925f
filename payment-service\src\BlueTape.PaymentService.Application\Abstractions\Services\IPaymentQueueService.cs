using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.PaymentService.Application.Models;

namespace BlueTape.PaymentService.Application.Abstractions.Services;

public interface IPaymentQueueService
{
    Task<IEnumerable<PaymentRequestModel>> GetDisbursementQueuesPaymentRequests(string provider, PaymentSubscriptionType subscriptionCode, CancellationToken ct);

    Task UpdateSequenceOrder(List<Guid> paymentRequestIds, string updatedBy, CancellationToken ct);
}
