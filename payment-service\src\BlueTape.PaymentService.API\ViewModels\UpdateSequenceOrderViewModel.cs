using System.ComponentModel.DataAnnotations;

namespace BlueTape.PaymentService.API.ViewModels;

public class UpdateSequenceOrderViewModel
{
    /// <summary>
    /// List of payment request IDs in their desired order
    /// </summary>
    [Required(ErrorMessage = "Payment request IDs are required")]
    [MinLength(1, ErrorMessage = "At least one payment request ID is required")]
    [MaxLength(200, ErrorMessage = "Cannot reorder more than 200 payments at once")]
    public List<Guid> PaymentRequestIds { get; set; } = new();
}
